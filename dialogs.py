"""
对话框模块
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
from datetime import datetime, timedelta
from config import config

class ModelImportDialog:
    """模型导入对话框"""
    
    def __init__(self, parent, file_path):
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("导入模型")
        self.dialog.geometry("500x550")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        self.setup_ui(file_path)
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def setup_ui(self, file_path):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 文件信息
        ttk_bs.Label(main_frame, text="文件路径:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        ttk_bs.Label(main_frame, text=file_path, wraplength=450).pack(anchor=W, pady=(0, 15))
        
        # 模型名称
        ttk_bs.Label(main_frame, text="模型名称:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.name_var = tk.StringVar(value=os.path.splitext(os.path.basename(file_path))[0])
        ttk_bs.Entry(main_frame, textvariable=self.name_var, width=50).pack(fill=X, pady=(0, 15))
        
        # 模型描述
        ttk_bs.Label(main_frame, text="模型描述:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.desc_text = tk.Text(main_frame, height=6, width=50)
        self.desc_text.pack(fill=BOTH, expand=True, pady=(0, 15))

        # 项目
        ttk_bs.Label(main_frame, text="项目:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.project_var = tk.StringVar(value="默认项目")
        project_frame = ttk_bs.Frame(main_frame)
        project_frame.pack(fill=X, pady=(0, 15))

        self.project_combo = ttk_bs.Combobox(project_frame, textvariable=self.project_var,
                                           values=["默认项目", "数据处理项目", "空间分析项目", "质量检查项目", "其他"])
        self.project_combo.pack(side=LEFT, fill=X, expand=True)

        # 模型分类
        ttk_bs.Label(main_frame, text="模型分类:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.category_var = tk.StringVar(value="默认")
        category_frame = ttk_bs.Frame(main_frame)
        category_frame.pack(fill=X, pady=(0, 15))

        self.category_combo = ttk_bs.Combobox(category_frame, textvariable=self.category_var,
                                             values=["默认", "数据转换", "空间分析", "质量检查", "其他"])
        self.category_combo.pack(side=LEFT, fill=X, expand=True)

        # 加密选项
        encrypt_frame = ttk_bs.LabelFrame(main_frame, text="安全设置", padding=10)
        encrypt_frame.pack(fill=X, pady=(0, 15))

        self.auto_encrypt_var = tk.BooleanVar(value=True)
        ttk_bs.Checkbutton(encrypt_frame, text="自动加密模型文件（推荐）",
                          variable=self.auto_encrypt_var).pack(anchor=W)

        ttk_bs.Label(encrypt_frame, text="启用后，导入的FMW文件将自动加密保护，防止知识产权泄露",
                    font=("", 8), foreground="gray").pack(anchor=W, pady=(5, 0))

        # 按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))
        
        ttk_bs.Button(button_frame, text="取消", bootstyle=SECONDARY,
                     command=self.cancel).pack(side=RIGHT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="确定", bootstyle=PRIMARY,
                     command=self.ok).pack(side=RIGHT)
    
    def ok(self):
        """确定按钮"""
        name = self.name_var.get().strip()
        if not name:
            messagebox.showwarning("警告", "请输入模型名称")
            return

        description = self.desc_text.get(1.0, tk.END).strip()
        project = self.project_var.get()
        category = self.category_var.get()
        auto_encrypt = self.auto_encrypt_var.get()

        self.result = (name, description, project, category, auto_encrypt)
        self.dialog.destroy()
    
    def cancel(self):
        """取消按钮"""
        self.dialog.destroy()

class SettingsDialog:
    """设置对话框"""
    
    def __init__(self, parent):
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        self.setup_ui()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def setup_ui(self):
        """设置界面"""
        # 创建选项卡
        notebook = ttk_bs.Notebook(self.dialog)
        notebook.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 基本设置选项卡
        self.create_basic_tab(notebook)
        
        # 路径设置选项卡
        self.create_path_tab(notebook)
        
        # 界面设置选项卡
        self.create_ui_tab(notebook)
        
        # 按钮框架
        button_frame = ttk_bs.Frame(self.dialog)
        button_frame.pack(fill=X, padx=10, pady=(0, 10))
        
        ttk_bs.Button(button_frame, text="取消", bootstyle=SECONDARY,
                     command=self.cancel).pack(side=RIGHT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="应用", bootstyle=SUCCESS,
                     command=self.apply).pack(side=RIGHT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="确定", bootstyle=PRIMARY,
                     command=self.ok).pack(side=RIGHT)
    
    def create_basic_tab(self, notebook):
        """创建基本设置选项卡"""
        basic_frame = ttk_bs.Frame(notebook)
        notebook.add(basic_frame, text="基本设置")
        
        # FME路径设置
        fme_frame = ttk_bs.LabelFrame(basic_frame, text="FME设置", padding=10)
        fme_frame.pack(fill=X, padx=10, pady=10)
        
        ttk_bs.Label(fme_frame, text="FME可执行文件路径:").pack(anchor=W, pady=(0, 5))
        
        path_frame = ttk_bs.Frame(fme_frame)
        path_frame.pack(fill=X, pady=(0, 10))
        
        self.fme_path_var = tk.StringVar(value=config.get("fme_path", ""))
        ttk_bs.Entry(path_frame, textvariable=self.fme_path_var).pack(side=LEFT, fill=X, expand=True)
        
        def browse_fme():
            path = filedialog.askopenfilename(
                title="选择FME可执行文件",
                filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
            )
            if path:
                self.fme_path_var.set(path)
        
        ttk_bs.Button(path_frame, text="浏览", command=browse_fme).pack(side=RIGHT, padx=(5, 0))
        
        # 自动备份设置
        backup_frame = ttk_bs.LabelFrame(basic_frame, text="备份设置", padding=10)
        backup_frame.pack(fill=X, padx=10, pady=10)
        
        self.auto_backup_var = tk.BooleanVar(value=config.get("auto_backup", True))
        ttk_bs.Checkbutton(backup_frame, text="自动备份模型文件",
                          variable=self.auto_backup_var).pack(anchor=W)
        
        # 最近文件设置
        recent_frame = ttk_bs.LabelFrame(basic_frame, text="最近文件", padding=10)
        recent_frame.pack(fill=X, padx=10, pady=10)
        
        ttk_bs.Label(recent_frame, text="最大最近文件数量:").pack(anchor=W, pady=(0, 5))
        self.max_recent_var = tk.StringVar(value=str(config.get("max_recent_files", 10)))
        ttk_bs.Entry(recent_frame, textvariable=self.max_recent_var, width=10).pack(anchor=W)
    
    def create_path_tab(self, notebook):
        """创建路径设置选项卡"""
        path_frame = ttk_bs.Frame(notebook)
        notebook.add(path_frame, text="路径设置")
        
        # 工作目录设置
        workspace_frame = ttk_bs.LabelFrame(path_frame, text="工作目录", padding=10)
        workspace_frame.pack(fill=X, padx=10, pady=10)
        
        self.workspace_var = tk.StringVar(value=config.get("workspace_dir", ""))
        self.create_path_setting(workspace_frame, "工作目录:", self.workspace_var)
        
        # 模型目录设置
        models_frame = ttk_bs.LabelFrame(path_frame, text="模型目录", padding=10)
        models_frame.pack(fill=X, padx=10, pady=10)
        
        self.models_var = tk.StringVar(value=config.get("models_dir", ""))
        self.create_path_setting(models_frame, "模型存储目录:", self.models_var)
        
        # 输出目录设置
        output_frame = ttk_bs.LabelFrame(path_frame, text="输出目录", padding=10)
        output_frame.pack(fill=X, padx=10, pady=10)
        
        self.output_var = tk.StringVar(value=config.get("output_dir", ""))
        self.create_path_setting(output_frame, "输出目录:", self.output_var)
        
        # 临时目录设置
        temp_frame = ttk_bs.LabelFrame(path_frame, text="临时目录", padding=10)
        temp_frame.pack(fill=X, padx=10, pady=10)
        
        self.temp_var = tk.StringVar(value=config.get("temp_dir", ""))
        self.create_path_setting(temp_frame, "临时目录:", self.temp_var)
    
    def create_path_setting(self, parent, label_text, var):
        """创建路径设置控件"""
        ttk_bs.Label(parent, text=label_text).pack(anchor=W, pady=(0, 5))
        
        path_frame = ttk_bs.Frame(parent)
        path_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Entry(path_frame, textvariable=var).pack(side=LEFT, fill=X, expand=True)
        
        def browse_dir():
            path = filedialog.askdirectory()
            if path:
                var.set(path)
        
        ttk_bs.Button(path_frame, text="浏览", command=browse_dir).pack(side=RIGHT, padx=(5, 0))
    
    def create_ui_tab(self, notebook):
        """创建界面设置选项卡"""
        ui_frame = ttk_bs.Frame(notebook)
        notebook.add(ui_frame, text="界面设置")
        
        # 主题设置
        theme_frame = ttk_bs.LabelFrame(ui_frame, text="主题设置", padding=10)
        theme_frame.pack(fill=X, padx=10, pady=10)
        
        ttk_bs.Label(theme_frame, text="界面主题:").pack(anchor=W, pady=(0, 5))
        
        self.theme_var = tk.StringVar(value=config.get("theme", "cosmo"))
        themes = ["cosmo", "flatly", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"]
        ttk_bs.Combobox(theme_frame, textvariable=self.theme_var, values=themes,
                       state="readonly").pack(anchor=W, pady=(0, 10))
        
        # 窗口大小设置
        size_frame = ttk_bs.LabelFrame(ui_frame, text="窗口设置", padding=10)
        size_frame.pack(fill=X, padx=10, pady=10)
        
        ttk_bs.Label(size_frame, text="默认窗口大小:").pack(anchor=W, pady=(0, 5))
        
        self.window_size_var = tk.StringVar(value=config.get("window_size", "1200x800"))
        size_options = ["1024x768", "1200x800", "1366x768", "1440x900", "1920x1080"]
        ttk_bs.Combobox(size_frame, textvariable=self.window_size_var, values=size_options).pack(anchor=W)
    
    def apply(self):
        """应用设置"""
        try:
            # 验证FME路径
            fme_path = self.fme_path_var.get().strip()
            if fme_path and not os.path.exists(fme_path):
                messagebox.showwarning("警告", "FME路径不存在")
                return
            
            # 验证最近文件数量
            try:
                max_recent = int(self.max_recent_var.get())
                if max_recent < 1 or max_recent > 100:
                    raise ValueError()
            except ValueError:
                messagebox.showwarning("警告", "最近文件数量必须是1-100之间的整数")
                return
            
            # 保存设置
            config.set("fme_path", fme_path)
            config.set("auto_backup", self.auto_backup_var.get())
            config.set("max_recent_files", max_recent)
            config.set("workspace_dir", self.workspace_var.get().strip())
            config.set("models_dir", self.models_var.get().strip())
            config.set("output_dir", self.output_var.get().strip())
            config.set("temp_dir", self.temp_var.get().strip())
            config.set("theme", self.theme_var.get())
            config.set("window_size", self.window_size_var.get())
            
            # 确保目录存在
            config.ensure_directories()
            
            messagebox.showinfo("成功", "设置已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def ok(self):
        """确定按钮"""
        self.apply()
        self.dialog.destroy()
    
    def cancel(self):
        """取消按钮"""
        self.dialog.destroy()

class TaskHistoryDialog:
    """任务历史对话框"""

    def __init__(self, parent, task_manager):
        self.task_manager = task_manager

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("任务历史")
        self.dialog.geometry("800x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()
        self.load_history()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=10)
        main_frame.pack(fill=BOTH, expand=True)

        # 创建任务历史列表
        columns = ("task_id", "model_name", "status", "start_time", "execution_time")
        self.history_tree = ttk_bs.Treeview(main_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.history_tree.heading("task_id", text="任务ID")
        self.history_tree.heading("model_name", text="模型名称")
        self.history_tree.heading("status", text="状态")
        self.history_tree.heading("start_time", text="开始时间")
        self.history_tree.heading("execution_time", text="执行时间")

        # 设置列宽
        self.history_tree.column("task_id", width=150)
        self.history_tree.column("model_name", width=200)
        self.history_tree.column("status", width=100)
        self.history_tree.column("start_time", width=150)
        self.history_tree.column("execution_time", width=100)

        # 添加滚动条
        scrollbar = ttk_bs.Scrollbar(main_frame, orient=VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.history_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # 按钮框架
        button_frame = ttk_bs.Frame(self.dialog)
        button_frame.pack(fill=X, padx=10, pady=(0, 10))

        ttk_bs.Button(button_frame, text="刷新", bootstyle=SECONDARY,
                     command=self.load_history).pack(side=LEFT)
        ttk_bs.Button(button_frame, text="清除历史", bootstyle=WARNING,
                     command=self.clear_history).pack(side=LEFT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="关闭", bootstyle=PRIMARY,
                     command=self.dialog.destroy).pack(side=RIGHT)

    def load_history(self):
        """加载任务历史"""
        # 清空现有项目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # 获取任务历史
        history = self.task_manager.get_task_history()

        for task in history:
            task_id = task.get('id', '')
            model_name = task.get('fmw_name', '')
            status = task.get('status', '')
            start_time = task.get('started_at', '')
            execution_time = f"{task.get('execution_time', 0):.2f}s"

            # 格式化开始时间
            if start_time:
                try:
                    dt = datetime.fromisoformat(start_time)
                    start_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    pass

            self.history_tree.insert("", "end", values=(
                task_id, model_name, status, start_time, execution_time
            ))

    def clear_history(self):
        """清除历史记录"""
        if messagebox.askyesno("确认", "确定要清除所有历史记录吗？"):
            self.task_manager.task_history.clear()
            self.load_history()

class TaskStopDialog:
    """任务停止对话框"""

    def __init__(self, parent, running_tasks, fmw_runner, task_manager):
        self.running_tasks = running_tasks
        self.fmw_runner = fmw_runner
        self.task_manager = task_manager

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("停止任务")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=10)
        main_frame.pack(fill=BOTH, expand=True)

        ttk_bs.Label(main_frame, text="选择要停止的任务:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 10))

        # 创建任务列表
        self.task_listbox = tk.Listbox(main_frame, selectmode=tk.MULTIPLE)
        self.task_listbox.pack(fill=BOTH, expand=True, pady=(0, 10))

        # 添加任务到列表
        for task_id in self.running_tasks:
            task_info = self.task_manager.get_task(task_id)
            if task_info:
                display_text = f"{task_id} - {task_info.get('fmw_name', '未知模型')}"
            else:
                display_text = task_id
            self.task_listbox.insert(tk.END, display_text)

        # 按钮框架
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(button_frame, text="全选", bootstyle=SECONDARY,
                     command=self.select_all).pack(side=LEFT)
        ttk_bs.Button(button_frame, text="取消", bootstyle=SECONDARY,
                     command=self.dialog.destroy).pack(side=RIGHT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="停止选中", bootstyle=DANGER,
                     command=self.stop_selected).pack(side=RIGHT)

    def select_all(self):
        """全选任务"""
        self.task_listbox.select_set(0, tk.END)

    def stop_selected(self):
        """停止选中的任务"""
        selected_indices = self.task_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要停止的任务")
            return

        selected_tasks = [self.running_tasks[i] for i in selected_indices]

        if messagebox.askyesno("确认", f"确定要停止 {len(selected_tasks)} 个任务吗？"):
            stopped_count = 0
            for task_id in selected_tasks:
                success, message = self.fmw_runner.stop_task(task_id)
                if success:
                    self.task_manager.update_task_status(task_id, 'stopped')
                    stopped_count += 1

            messagebox.showinfo("完成", f"已停止 {stopped_count} 个任务")
            self.dialog.destroy()

class ClientCreateDialog:
    """客户端创建对话框"""

    def __init__(self, parent, models, client_generator):
        self.models = models
        self.client_generator = client_generator

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("创建分发客户端")
        self.dialog.geometry("700x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        # 创建选项卡
        notebook = ttk_bs.Notebook(self.dialog)
        notebook.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # 基本信息选项卡
        self.create_basic_tab(notebook)

        # 模型选择选项卡
        self.create_models_tab(notebook)

        # 许可证设置选项卡
        self.create_license_tab(notebook)

        # 按钮框架
        button_frame = ttk_bs.Frame(self.dialog)
        button_frame.pack(fill=X, padx=10, pady=(0, 10))

        ttk_bs.Button(button_frame, text="取消", bootstyle=SECONDARY,
                     command=self.dialog.destroy).pack(side=RIGHT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="创建客户端", bootstyle=PRIMARY,
                     command=self.create_client).pack(side=RIGHT)

    def create_basic_tab(self, notebook):
        """创建基本信息选项卡"""
        basic_frame = ttk_bs.Frame(notebook)
        notebook.add(basic_frame, text="基本信息")

        main_frame = ttk_bs.Frame(basic_frame, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # 客户端名称
        ttk_bs.Label(main_frame, text="客户端名称:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.client_name_var = tk.StringVar()
        ttk_bs.Entry(main_frame, textvariable=self.client_name_var, width=50).pack(fill=X, pady=(0, 15))

        # 客户端描述
        ttk_bs.Label(main_frame, text="客户端描述:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 5))
        self.client_desc_text = tk.Text(main_frame, height=6, width=50)
        self.client_desc_text.pack(fill=BOTH, expand=True, pady=(0, 15))

    def create_models_tab(self, notebook):
        """创建模型选择选项卡"""
        models_frame = ttk_bs.Frame(notebook)
        notebook.add(models_frame, text="模型选择")

        main_frame = ttk_bs.Frame(models_frame, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        ttk_bs.Label(main_frame, text="选择要包含的模型:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 10))

        # 创建模型列表
        list_frame = ttk_bs.Frame(main_frame)
        list_frame.pack(fill=BOTH, expand=True)

        self.models_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE)
        models_scrollbar = ttk_bs.Scrollbar(list_frame, orient=VERTICAL, command=self.models_listbox.yview)
        self.models_listbox.configure(yscrollcommand=models_scrollbar.set)

        self.models_listbox.pack(side=LEFT, fill=BOTH, expand=True)
        models_scrollbar.pack(side=RIGHT, fill=Y)

        # 添加模型到列表
        self.model_ids = []
        for model_id, model_info in self.models.items():
            display_text = f"{model_info['name']} ({model_info['category']})"
            if model_info.get('is_encrypted', False):
                display_text += " [已加密]"

            self.models_listbox.insert(tk.END, display_text)
            self.model_ids.append(model_id)

        # 选择按钮
        select_frame = ttk_bs.Frame(main_frame)
        select_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(select_frame, text="全选", bootstyle=SECONDARY,
                     command=self.select_all_models).pack(side=LEFT)
        ttk_bs.Button(select_frame, text="全不选", bootstyle=SECONDARY,
                     command=self.deselect_all_models).pack(side=LEFT, padx=(5, 0))

    def create_license_tab(self, notebook):
        """创建许可证设置选项卡"""
        license_frame = ttk_bs.Frame(notebook)
        notebook.add(license_frame, text="许可证设置")

        main_frame = ttk_bs.Frame(license_frame, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # 过期时间设置
        expire_frame = ttk_bs.LabelFrame(main_frame, text="过期时间", padding=10)
        expire_frame.pack(fill=X, pady=(0, 15))

        self.expire_enabled_var = tk.BooleanVar()
        ttk_bs.Checkbutton(expire_frame, text="启用过期时间限制",
                          variable=self.expire_enabled_var,
                          command=self.toggle_expire).pack(anchor=W, pady=(0, 10))

        expire_input_frame = ttk_bs.Frame(expire_frame)
        expire_input_frame.pack(fill=X)

        ttk_bs.Label(expire_input_frame, text="过期天数:").pack(side=LEFT)
        self.expire_days_var = tk.StringVar(value="30")
        self.expire_days_entry = ttk_bs.Entry(expire_input_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=LEFT, padx=(5, 0))
        self.expire_days_entry.config(state=tk.DISABLED)

        # 使用次数限制
        usage_frame = ttk_bs.LabelFrame(main_frame, text="使用次数", padding=10)
        usage_frame.pack(fill=X, pady=(0, 15))

        self.usage_enabled_var = tk.BooleanVar()
        ttk_bs.Checkbutton(usage_frame, text="启用使用次数限制",
                          variable=self.usage_enabled_var,
                          command=self.toggle_usage).pack(anchor=W, pady=(0, 10))

        usage_input_frame = ttk_bs.Frame(usage_frame)
        usage_input_frame.pack(fill=X)

        ttk_bs.Label(usage_input_frame, text="最大使用次数:").pack(side=LEFT)
        self.max_uses_var = tk.StringVar(value="100")
        self.max_uses_entry = ttk_bs.Entry(usage_input_frame, textvariable=self.max_uses_var, width=10)
        self.max_uses_entry.pack(side=LEFT, padx=(5, 0))
        self.max_uses_entry.config(state=tk.DISABLED)

        # 机器码限制
        machine_frame = ttk_bs.LabelFrame(main_frame, text="机器码限制", padding=10)
        machine_frame.pack(fill=X, pady=(0, 15))

        self.machine_enabled_var = tk.BooleanVar()
        ttk_bs.Checkbutton(machine_frame, text="启用机器码限制",
                          variable=self.machine_enabled_var,
                          command=self.toggle_machine).pack(anchor=W, pady=(0, 10))

        machine_input_frame = ttk_bs.Frame(machine_frame)
        machine_input_frame.pack(fill=X)

        ttk_bs.Label(machine_input_frame, text="允许的机器码 (每行一个):").pack(anchor=W, pady=(0, 5))
        self.machine_text = tk.Text(machine_input_frame, height=4, width=50)
        self.machine_text.pack(fill=X)
        self.machine_text.config(state=tk.DISABLED)

    def toggle_expire(self):
        """切换过期时间设置"""
        if self.expire_enabled_var.get():
            self.expire_days_entry.config(state=tk.NORMAL)
        else:
            self.expire_days_entry.config(state=tk.DISABLED)

    def toggle_usage(self):
        """切换使用次数设置"""
        if self.usage_enabled_var.get():
            self.max_uses_entry.config(state=tk.NORMAL)
        else:
            self.max_uses_entry.config(state=tk.DISABLED)

    def toggle_machine(self):
        """切换机器码设置"""
        if self.machine_enabled_var.get():
            self.machine_text.config(state=tk.NORMAL)
        else:
            self.machine_text.config(state=tk.DISABLED)

    def select_all_models(self):
        """全选模型"""
        self.models_listbox.select_set(0, tk.END)

    def deselect_all_models(self):
        """全不选模型"""
        self.models_listbox.selection_clear(0, tk.END)

    def create_client(self):
        """创建客户端"""
        # 验证输入
        client_name = self.client_name_var.get().strip()
        if not client_name:
            messagebox.showwarning("警告", "请输入客户端名称")
            return

        # 获取选中的模型
        selected_indices = self.models_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return

        selected_models = [self.model_ids[i] for i in selected_indices]

        # 构建许可证配置
        license_config = {}

        if self.expire_enabled_var.get():
            try:
                expire_days = int(self.expire_days_var.get())
                license_config["expire_days"] = expire_days
            except ValueError:
                messagebox.showwarning("警告", "过期天数必须是整数")
                return

        if self.usage_enabled_var.get():
            try:
                max_uses = int(self.max_uses_var.get())
                license_config["max_uses"] = max_uses
            except ValueError:
                messagebox.showwarning("警告", "最大使用次数必须是整数")
                return

        if self.machine_enabled_var.get():
            machine_codes = self.machine_text.get(1.0, tk.END).strip().split('\n')
            machine_codes = [code.strip() for code in machine_codes if code.strip()]
            if machine_codes:
                license_config["allowed_machines"] = machine_codes

        try:
            # 创建客户端
            success, message, zip_path = self.client_generator.generate_client(
                client_name, selected_models, license_config
            )

            if success:
                messagebox.showinfo("成功", f"客户端创建成功！\n{message}")
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", f"客户端创建失败：\n{message}")

        except Exception as e:
            messagebox.showerror("错误", f"创建客户端时发生错误：\n{e}")

class ClientManageDialog:
    """客户端管理对话框"""

    def __init__(self, parent, client_generator):
        self.client_generator = client_generator

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("客户端管理")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()
        self.load_clients()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=10)
        main_frame.pack(fill=BOTH, expand=True)

        ttk_bs.Label(main_frame, text="已生成的客户端:", font=("", 10, "bold")).pack(anchor=W, pady=(0, 10))

        # 创建客户端列表
        list_frame = ttk_bs.Frame(main_frame)
        list_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        self.clients_listbox = tk.Listbox(list_frame)
        clients_scrollbar = ttk_bs.Scrollbar(list_frame, orient=VERTICAL, command=self.clients_listbox.yview)
        self.clients_listbox.configure(yscrollcommand=clients_scrollbar.set)

        self.clients_listbox.pack(side=LEFT, fill=BOTH, expand=True)
        clients_scrollbar.pack(side=RIGHT, fill=Y)

        # 按钮框架
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X)

        ttk_bs.Button(button_frame, text="刷新", bootstyle=SECONDARY,
                     command=self.load_clients).pack(side=LEFT)
        ttk_bs.Button(button_frame, text="打开目录", bootstyle=INFO,
                     command=self.open_clients_dir).pack(side=LEFT, padx=(5, 0))
        ttk_bs.Button(button_frame, text="关闭", bootstyle=PRIMARY,
                     command=self.dialog.destroy).pack(side=RIGHT)

    def load_clients(self):
        """加载客户端列表"""
        self.clients_listbox.delete(0, tk.END)

        clients = self.client_generator.get_client_list()
        for client in clients:
            self.clients_listbox.insert(tk.END, client)

    def open_clients_dir(self):
        """打开客户端目录"""
        try:
            clients_dir = config.get("clients_dir")
            if os.path.exists(clients_dir):
                os.startfile(clients_dir)
            else:
                messagebox.showwarning("警告", "客户端目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开目录失败: {e}")

class RegistrationCodeDialog:
    """注册码生成对话框"""

    def __init__(self, parent):
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("生成注册码")
        self.dialog.geometry("700x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.setup_ui()

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # 标题
        ttk_bs.Label(main_frame, text="客户端注册码生成器",
                    font=("Arial", 16, "bold")).pack(pady=(0, 20))

        # 机器码输入
        machine_frame = ttk_bs.LabelFrame(main_frame, text="机器码信息", padding=15)
        machine_frame.pack(fill=X, pady=(0, 15))

        ttk_bs.Label(machine_frame, text="客户端机器码:",
                    font=("Arial", 10, "bold")).pack(anchor=W, pady=(0, 5))

        self.machine_id_var = tk.StringVar()
        machine_entry = ttk_bs.Entry(machine_frame, textvariable=self.machine_id_var,
                                    font=("Consolas", 10))
        machine_entry.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(machine_frame, text="请输入客户端提供的32位机器码",
                    font=("Arial", 9), foreground="gray").pack(anchor=W)

        # 许可证设置
        license_frame = ttk_bs.LabelFrame(main_frame, text="许可证设置", padding=15)
        license_frame.pack(fill=X, pady=(0, 15))

        # 客户端名称
        name_frame = ttk_bs.Frame(license_frame)
        name_frame.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(name_frame, text="客户端名称:").pack(side=LEFT)
        self.client_name_var = tk.StringVar(value="FME客户端")
        ttk_bs.Entry(name_frame, textvariable=self.client_name_var, width=30).pack(side=LEFT, padx=(10, 0))

        # 过期时间
        expire_frame = ttk_bs.Frame(license_frame)
        expire_frame.pack(fill=X, pady=(0, 10))

        self.enable_expire_var = tk.BooleanVar(value=True)
        ttk_bs.Checkbutton(expire_frame, text="启用过期时间",
                          variable=self.enable_expire_var,
                          command=self.toggle_expire).pack(side=LEFT)

        ttk_bs.Label(expire_frame, text="天数:").pack(side=LEFT, padx=(20, 0))
        self.expire_days_var = tk.StringVar(value="365")
        self.expire_days_entry = ttk_bs.Entry(expire_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=LEFT, padx=(5, 0))

        # 使用次数
        usage_frame = ttk_bs.Frame(license_frame)
        usage_frame.pack(fill=X, pady=(0, 10))

        self.enable_usage_var = tk.BooleanVar()
        ttk_bs.Checkbutton(usage_frame, text="启用使用次数限制",
                          variable=self.enable_usage_var,
                          command=self.toggle_usage).pack(side=LEFT)

        ttk_bs.Label(usage_frame, text="次数:").pack(side=LEFT, padx=(20, 0))
        self.max_uses_var = tk.StringVar(value="1000")
        self.max_uses_entry = ttk_bs.Entry(usage_frame, textvariable=self.max_uses_var, width=10)
        self.max_uses_entry.pack(side=LEFT, padx=(5, 0))
        self.max_uses_entry.config(state=DISABLED)

        # 生成按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(15, 0))

        ttk_bs.Button(button_frame, text="生成注册码", bootstyle=PRIMARY,
                     command=self.generate_code).pack(side=LEFT)

        ttk_bs.Button(button_frame, text="清空", bootstyle=SECONDARY,
                     command=self.clear_form).pack(side=LEFT, padx=(10, 0))

        ttk_bs.Button(button_frame, text="关闭", bootstyle=SECONDARY,
                     command=self.close).pack(side=RIGHT)

        # 结果显示
        result_frame = ttk_bs.LabelFrame(main_frame, text="生成的注册码", padding=15)
        result_frame.pack(fill=BOTH, expand=True, pady=(15, 0))

        self.result_text = tk.Text(result_frame, wrap=WORD, font=("Consolas", 9))
        result_scrollbar = ttk_bs.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)

        self.result_text.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")

        # 复制按钮
        copy_frame = ttk_bs.Frame(result_frame)
        copy_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(copy_frame, text="复制注册码",
                     command=self.copy_code).pack(side=LEFT)

        ttk_bs.Button(copy_frame, text="保存到文件",
                     command=self.save_code).pack(side=LEFT, padx=(10, 0))

    def toggle_expire(self):
        """切换过期时间设置"""
        if self.enable_expire_var.get():
            self.expire_days_entry.config(state=NORMAL)
        else:
            self.expire_days_entry.config(state=DISABLED)

    def toggle_usage(self):
        """切换使用次数设置"""
        if self.enable_usage_var.get():
            self.max_uses_entry.config(state=NORMAL)
        else:
            self.max_uses_entry.config(state=DISABLED)

    def generate_code(self):
        """生成注册码"""
        machine_id = self.machine_id_var.get().strip()
        if not machine_id:
            messagebox.showwarning("警告", "请输入客户端机器码")
            return

        if len(machine_id) != 32:  # MD5哈希长度
            messagebox.showwarning("警告", "机器码格式不正确（应为32位十六进制字符串）")
            return

        try:
            import json
            import base64
            import uuid
            from datetime import datetime, timedelta

            # 构建许可证数据
            license_data = {
                "license_id": str(uuid.uuid4()),
                "client_name": self.client_name_var.get(),
                "created_at": datetime.now().isoformat(),
                "allowed_machines": [machine_id],
                "current_uses": 0,
                "is_active": True
            }

            # 添加过期时间
            if self.enable_expire_var.get():
                try:
                    days = int(self.expire_days_var.get())
                    expire_date = datetime.now() + timedelta(days=days)
                    license_data["expire_date"] = expire_date.isoformat()
                except ValueError:
                    messagebox.showerror("错误", "过期天数必须是整数")
                    return

            # 添加使用次数限制
            if self.enable_usage_var.get():
                try:
                    max_uses = int(self.max_uses_var.get())
                    license_data["max_uses"] = max_uses
                except ValueError:
                    messagebox.showerror("错误", "使用次数必须是整数")
                    return

            # 生成注册码（Base64编码的JSON）
            license_json = json.dumps(license_data, ensure_ascii=False)
            registration_code = base64.b64encode(license_json.encode()).decode()

            # 显示结果
            result_text = f"""注册码生成成功！

许可证信息:
• 许可证ID: {license_data['license_id']}
• 客户端名称: {license_data['client_name']}
• 绑定机器码: {machine_id}
• 创建时间: {license_data['created_at']}
• 过期时间: {license_data.get('expire_date', '无限制')}
• 最大使用次数: {license_data.get('max_uses', '无限制')}

注册码:
{registration_code}

使用说明:
1. 将上述注册码提供给客户端用户
2. 用户在客户端中输入此注册码进行激活
3. 注册码与机器码绑定，只能在指定机器上使用
4. 请妥善保管注册码，避免泄露
"""

            self.result_text.delete(1.0, END)
            self.result_text.insert(1.0, result_text)

            # 保存注册码用于复制
            self.current_code = registration_code

        except Exception as e:
            messagebox.showerror("错误", f"生成注册码失败: {e}")

    def copy_code(self):
        """复制注册码"""
        if hasattr(self, 'current_code'):
            self.dialog.clipboard_clear()
            self.dialog.clipboard_append(self.current_code)
            messagebox.showinfo("提示", "注册码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "请先生成注册码")

    def save_code(self):
        """保存注册码到文件"""
        if not hasattr(self, 'current_code'):
            messagebox.showwarning("警告", "请先生成注册码")
            return

        from tkinter import filedialog
        from datetime import datetime

        file_path = filedialog.asksaveasfilename(
            title="保存注册码",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialvalue=f"registration_code_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.get(1.0, END))
                messagebox.showinfo("成功", f"注册码已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存注册码失败: {e}")

    def clear_form(self):
        """清空表单"""
        self.machine_id_var.set("")
        self.client_name_var.set("FME客户端")
        self.enable_expire_var.set(True)
        self.expire_days_var.set("365")
        self.enable_usage_var.set(False)
        self.max_uses_var.set("1000")

        self.toggle_expire()
        self.toggle_usage()

        self.result_text.delete(1.0, END)

        if hasattr(self, 'current_code'):
            delattr(self, 'current_code')

    def close(self):
        """关闭对话框"""
        self.dialog.destroy()
