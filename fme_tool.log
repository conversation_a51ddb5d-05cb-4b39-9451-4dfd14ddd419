2025-08-12 15:08:36,221 - __main__ - INFO - 启动FME模型管理工具
2025-08-12 15:08:36,300 - __main__ - ERROR - 程序运行出错: 'ModelManager' object has no attribute 'json_manager'
2025-08-12 15:08:36,305 - __main__ - ERROR - Traceback (most recent call last):
  File "E:\YC\每日任务\2025\08\0812\fme管理工具\main.py", line 43, in main
    app = MainWindow()
  File "E:\YC\每日任务\2025\08\0812\fme管理工具\main_window.py", line 31, in __init__
    self.model_manager = ModelManager()
                         ~~~~~~~~~~~~^^
  File "E:\YC\每日任务\2025\08\0812\fme管理工具\model_manager.py", line 22, in __init__
    self.models_index = self.load_models_index()
                        ~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\YC\每日任务\2025\08\0812\fme管理工具\model_manager.py", line 33, in load_models_index
    models_index = self.json_manager.load_config("models_index", default_index)
                   ^^^^^^^^^^^^^^^^^
AttributeError: 'ModelManager' object has no attribute 'json_manager'

