"""
FME模型管理工具主窗口
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import threading
from datetime import datetime, timedelta

from config import config
from model_manager import ModelManager
from fmw_runner import FM<PERSON>unner, TaskManager
from client_generator import ClientGenerator
from parse_fmw import parse_fmw_parameters
from dialogs import (ModelImportDialog, SettingsDialog, TaskHistoryDialog,
                    TaskStopDialog, ClientCreateDialog, ClientManageDialog)

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        # 初始化主题
        self.style = ttk_bs.Style(theme=config.get("theme", "cosmo"))
        self.root = self.style.master
        self.root.title(f"FME模型管理工具 v{config.get('app_version', '1.0.0')}")
        self.root.geometry(config.get("window_size", "1200x800"))
        
        # 初始化管理器
        self.model_manager = ModelManager()
        self.fmw_runner = FMWRunner()
        self.task_manager = TaskManager()
        self.client_generator = ClientGenerator()
        
        # 确保必要目录存在
        config.ensure_directories()
        
        # 设置界面
        self.setup_ui()
        self.refresh_models()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主菜单
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入模型", command=self.import_model)
        file_menu.add_command(label="导出模型", command=self.export_model)
        file_menu.add_separator()
        file_menu.add_command(label="设置", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 模型菜单
        model_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="模型", menu=model_menu)
        model_menu.add_command(label="刷新列表", command=self.refresh_models)
        model_menu.add_command(label="解密模型", command=self.decrypt_model)
        model_menu.add_separator()
        model_menu.add_command(label="删除模型", command=self.delete_model)
        
        # 运行菜单
        run_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="运行", menu=run_menu)
        run_menu.add_command(label="运行模型", command=self.run_model)
        run_menu.add_command(label="停止任务", command=self.stop_task)
        run_menu.add_separator()
        run_menu.add_command(label="任务历史", command=self.show_task_history)
        
        # 分发菜单
        distribute_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="分发", menu=distribute_menu)
        distribute_menu.add_command(label="创建客户端", command=self.create_client)
        distribute_menu.add_command(label="许可证生成器", command=self.open_multi_model_license_generator)
        distribute_menu.add_separator()
        distribute_menu.add_command(label="客户端管理", command=self.manage_clients)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="安装PyInstaller", command=self.install_pyinstaller)
        tools_menu.add_separator()
        tools_menu.add_command(label="清理临时文件", command=self.clean_temp_files)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk_bs.Frame(self.root)
        toolbar.pack(fill=X, padx=5, pady=2)
        
        # 导入按钮
        ttk_bs.Button(toolbar, text="导入模型", bootstyle=PRIMARY,
                     command=self.import_model).pack(side=LEFT, padx=2)
        
        # 运行按钮
        ttk_bs.Button(toolbar, text="运行模型", bootstyle=SUCCESS,
                     command=self.run_model).pack(side=LEFT, padx=2)
        

        
        # 分发按钮
        ttk_bs.Button(toolbar, text="创建客户端", bootstyle=INFO,
                     command=self.create_client).pack(side=LEFT, padx=2)
        
        # 分隔符
        ttk_bs.Separator(toolbar, orient=VERTICAL).pack(side=LEFT, fill=Y, padx=5)
        
        # 刷新按钮
        ttk_bs.Button(toolbar, text="刷新", bootstyle=SECONDARY,
                     command=self.refresh_models).pack(side=LEFT, padx=2)
        
        # 设置按钮
        ttk_bs.Button(toolbar, text="设置", bootstyle=SECONDARY,
                     command=self.open_settings).pack(side=RIGHT, padx=2)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建主面板
        main_paned = ttk_bs.PanedWindow(self.root, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板 - 模型列表
        left_frame = ttk_bs.LabelFrame(main_paned, text="模型列表", padding=10)
        main_paned.add(left_frame, weight=1)
        
        # 搜索框
        search_frame = ttk_bs.Frame(left_frame)
        search_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(search_frame, text="搜索:").pack(side=LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = ttk_bs.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=LEFT, fill=X, expand=True, padx=(5, 0))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        # 分类筛选
        category_frame = ttk_bs.Frame(left_frame)
        category_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(category_frame, text="分类:").pack(side=LEFT)
        self.category_var = tk.StringVar()
        self.category_combo = ttk_bs.Combobox(category_frame, textvariable=self.category_var,
                                             values=["全部", "默认"], state="readonly")
        self.category_combo.pack(side=LEFT, fill=X, expand=True, padx=(5, 0))
        self.category_combo.set("全部")
        self.category_combo.bind('<<ComboboxSelected>>', self.on_category_change)
        
        # 模型列表
        self.create_model_list(left_frame)
        
        # 右侧面板 - 详细信息和操作
        right_frame = ttk_bs.LabelFrame(main_paned, text="模型详情", padding=10)
        main_paned.add(right_frame, weight=2)
        
        # 创建选项卡
        self.notebook = ttk_bs.Notebook(right_frame)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # 模型信息选项卡
        self.create_model_info_tab()
        
        # 参数设置选项卡
        self.create_parameters_tab()
        
        # 运行日志选项卡
        self.create_log_tab()
    
    def create_model_list(self, parent):
        """创建模型列表"""
        # 创建Treeview
        columns = ("name", "category", "size", "created", "encrypted", "usage")
        self.model_tree = ttk_bs.Treeview(parent, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.model_tree.heading("name", text="模型名称")
        self.model_tree.heading("category", text="分类")
        self.model_tree.heading("size", text="大小")
        self.model_tree.heading("created", text="创建时间")
        self.model_tree.heading("encrypted", text="加密状态")
        self.model_tree.heading("usage", text="使用次数")
        
        # 设置列宽
        self.model_tree.column("name", width=200)
        self.model_tree.column("category", width=80)
        self.model_tree.column("size", width=80)
        self.model_tree.column("created", width=120)
        self.model_tree.column("encrypted", width=80)
        self.model_tree.column("usage", width=80)
        
        # 添加滚动条
        scrollbar = ttk_bs.Scrollbar(parent, orient=VERTICAL, command=self.model_tree.yview)
        self.model_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.model_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定选择事件
        self.model_tree.bind('<<TreeviewSelect>>', self.on_model_select)
        
        # 绑定右键菜单
        self.model_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_model_info_tab(self):
        """创建模型信息选项卡"""
        info_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(info_frame, text="模型信息")
        
        # 创建信息显示区域
        self.info_text = tk.Text(info_frame, height=20, wrap=tk.WORD, state=tk.DISABLED)
        info_scrollbar = ttk_bs.Scrollbar(info_frame, orient=VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=LEFT, fill=BOTH, expand=True)
        info_scrollbar.pack(side=RIGHT, fill=Y)
    
    def create_parameters_tab(self):
        """创建参数设置选项卡"""
        params_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(params_frame, text="参数设置")
        
        # 创建滚动框架
        self.params_canvas = tk.Canvas(params_frame)
        params_scrollbar = ttk_bs.Scrollbar(params_frame, orient=VERTICAL, command=self.params_canvas.yview)
        self.params_scrollable_frame = ttk_bs.Frame(self.params_canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )
        
        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_canvas.pack(side=LEFT, fill=BOTH, expand=True)
        params_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 参数输入控件字典
        self.param_widgets = {}
    
    def create_log_tab(self):
        """创建运行日志选项卡"""
        log_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(log_frame, text="运行日志")
        
        # 创建日志显示区域
        self.log_text = tk.Text(log_frame, height=20, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk_bs.Scrollbar(log_frame, orient=VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=LEFT, fill=BOTH, expand=True)
        log_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加清除按钮
        clear_button = ttk_bs.Button(log_frame, text="清除日志", command=self.clear_log)
        clear_button.pack(side=BOTTOM, pady=5)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk_bs.Frame(self.root)
        self.status_bar.pack(fill=X, side=BOTTOM)
        
        self.status_label = ttk_bs.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=LEFT, padx=5)
        
        # 进度条
        self.progress_bar = ttk_bs.Progressbar(self.status_bar, mode='indeterminate')
        self.progress_bar.pack(side=RIGHT, padx=5)
    
    def refresh_models(self):
        """刷新模型列表"""
        # 清空现有项目
        for item in self.model_tree.get_children():
            self.model_tree.delete(item)
        
        # 获取所有模型
        models = self.model_manager.get_all_models()
        
        # 更新分类列表
        categories = set(["全部"])
        for model_info in models.values():
            categories.add(model_info.get("category", "默认"))
        
        self.category_combo['values'] = list(categories)
        
        # 添加模型到列表
        for model_id, model_info in models.items():
            # 格式化大小
            size_mb = model_info.get("file_size", 0) / (1024 * 1024)
            size_str = f"{size_mb:.2f} MB"
            
            # 格式化创建时间
            created_at = model_info.get("created_at", "")
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at)
                    created_str = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    created_str = created_at
            else:
                created_str = ""
            
            # 加密状态
            encrypted_str = "是" if model_info.get("is_encrypted", False) else "否"
            
            # 使用次数
            usage_count = model_info.get("usage_count", 0)
            
            self.model_tree.insert("", "end", iid=model_id, values=(
                model_info.get("name", ""),
                model_info.get("category", "默认"),
                size_str,
                created_str,
                encrypted_str,
                usage_count
            ))
        
        self.update_status(f"已加载 {len(models)} 个模型")
    
    def on_search(self, event=None):
        """搜索事件处理"""
        keyword = self.search_var.get()
        category = self.category_var.get()
        
        # 清空现有项目
        for item in self.model_tree.get_children():
            self.model_tree.delete(item)
        
        # 搜索模型
        if category == "全部":
            category = ""
        
        models = self.model_manager.search_models(keyword, category)
        
        # 添加搜索结果到列表
        for model_id, model_info in models.items():
            size_mb = model_info.get("file_size", 0) / (1024 * 1024)
            size_str = f"{size_mb:.2f} MB"
            
            created_at = model_info.get("created_at", "")
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at)
                    created_str = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    created_str = created_at
            else:
                created_str = ""
            
            encrypted_str = "是" if model_info.get("is_encrypted", False) else "否"
            usage_count = model_info.get("usage_count", 0)
            
            self.model_tree.insert("", "end", iid=model_id, values=(
                model_info.get("name", ""),
                model_info.get("category", "默认"),
                size_str,
                created_str,
                encrypted_str,
                usage_count
            ))
    
    def on_category_change(self, event=None):
        """分类变化事件处理"""
        self.on_search()
    
    def on_model_select(self, event=None):
        """模型选择事件处理"""
        selection = self.model_tree.selection()
        if not selection:
            return
        
        model_id = selection[0]
        model_info = self.model_manager.get_model_info(model_id)
        
        if model_info:
            self.show_model_info(model_info)
            self.load_model_parameters(model_info)
    
    def show_model_info(self, model_info):
        """显示模型信息"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        # 显示相对路径，但标注为相对于程序目录
        relative_path = model_info.get('file_path', '')
        display_path = f"{relative_path} (相对于程序目录)"

        info_text = f"""模型名称: {model_info.get('name', '')}
模型ID: {model_info.get('id', '')}
分类: {model_info.get('category', '')}
描述: {model_info.get('description', '')}
原始文件名: {model_info.get('original_filename', '')}
存储文件名: {model_info.get('hidden_filename', '')}
文件路径: {display_path}
文件大小: {model_info.get('file_size', 0) / (1024 * 1024):.2f} MB
文件哈希: {model_info.get('file_hash', '')}
创建时间: {model_info.get('created_at', '')}
更新时间: {model_info.get('updated_at', '')}
加密状态: {'已加密' if model_info.get('is_encrypted', False) else '未加密'}
使用次数: {model_info.get('usage_count', 0)}
最后使用: {model_info.get('last_used', '从未使用')}

参数信息:
参数数量: {len(model_info.get('parameters', []))}
"""
        
        # 添加参数详情
        for param in model_info.get('parameters', []):
            param_name = param.get('name', '')
            param_info = param.get('info', {})
            info_text += f"\n- {param_name}: {param_info.get('prompt', '')}"
            info_text += f" (类型: {param_info.get('type', '')}, 必填: {'是' if param_info.get('required', False) else '否'})"
        
        self.info_text.insert(1.0, info_text)
        self.info_text.config(state=tk.DISABLED)
    
    def load_model_parameters(self, model_info):
        """加载模型参数"""
        # 清空现有参数控件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()

        self.param_widgets.clear()

        parameters = model_info.get('parameters', [])
        if not parameters:
            ttk_bs.Label(self.params_scrollable_frame, text="此模型没有可配置的参数").pack(pady=20)
            return

        # 首先尝试重新解析FMW文件获取最新参数
        try:
            fmw_path = model_info.get('file_path', '')
            if fmw_path and os.path.exists(fmw_path):
                from parse_fmw import parse_fmw_parameters
                parsed_params = parse_fmw_parameters(fmw_path, debug=False)
                if parsed_params and parsed_params.get('parameters'):
                    parameters = parsed_params['parameters']
                    print(f"重新解析到 {len(parameters)} 个参数")
        except Exception as e:
            print(f"重新解析参数失败: {e}")

        row = 0
        for param in parameters:
            param_name = param.get('name', '')
            param_info = param.get('info', {})

            if not param_name:
                continue

            # 创建参数标签
            label_text = param_info.get('prompt', param_name)
            if param_info.get('required', False):
                label_text += " *"

            label = ttk_bs.Label(self.params_scrollable_frame, text=label_text)
            label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)

            # 根据参数类型创建输入控件
            widget = self.create_parameter_widget(self.params_scrollable_frame, param_info)
            widget.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

            # 保存控件引用
            self.param_widgets[param_name] = {
                'widget': widget,
                'info': param_info,
                'label': label
            }

            row += 1

        # 配置列权重
        self.params_scrollable_frame.columnconfigure(1, weight=1)

        # 添加运行按钮
        run_frame = ttk_bs.Frame(self.params_scrollable_frame)
        run_frame.grid(row=row, column=0, columnspan=2, pady=20)

        ttk_bs.Button(run_frame, text="运行模型", bootstyle=SUCCESS,
                     command=self.run_model).pack(side=LEFT, padx=5)

        ttk_bs.Button(run_frame, text="重置参数", bootstyle=SECONDARY,
                     command=lambda: self.load_model_parameters(model_info)).pack(side=LEFT, padx=5)
    
    def create_parameter_widget(self, parent, param_info):
        """根据参数类型创建输入控件"""
        param_type = param_info.get('type', 'text')
        default_value = param_info.get('default_value', '')
        access_mode = param_info.get('access_mode', 'read')

        print(f"创建参数控件: 类型={param_type}, 默认值={default_value}, 访问模式={access_mode}")

        # 文件/文件夹选择参数
        if param_type == 'file':
            frame = ttk_bs.Frame(parent)

            var = tk.StringVar(value=default_value)
            entry = ttk_bs.Entry(frame, textvariable=var)
            entry.pack(side=LEFT, fill=X, expand=True)

            def browse_file():
                if param_info.get('is_folder', False) or access_mode == 'write':
                    # 文件夹选择或输出路径
                    path = filedialog.askdirectory(title="选择文件夹")
                else:
                    # 文件选择
                    file_types = param_info.get('file_types', ['*.*'])
                    if file_types and file_types != ['*.*']:
                        # 构建文件类型过滤器
                        filetypes = []
                        for ft in file_types:
                            if ft.startswith('*.'):
                                ext = ft[2:]
                                filetypes.append((f"{ext.upper()}文件", ft))
                        filetypes.append(("所有文件", "*.*"))
                    else:
                        filetypes = [("所有文件", "*.*")]

                    path = filedialog.askopenfilename(title="选择文件", filetypes=filetypes)

                if path:
                    var.set(path)

            ttk_bs.Button(frame, text="浏览", command=browse_file).pack(side=RIGHT, padx=(5, 0))
            frame.var = var  # 保存变量引用
            return frame

        # 下拉选择参数
        elif param_type in ['dropdown', 'listbox']:
            options = param_info.get('options', [])
            if options:
                values = []
                for opt in options:
                    if isinstance(opt, dict):
                        values.append(opt.get('value', opt.get('label', '')))
                    else:
                        values.append(str(opt))

                if param_type == 'listbox' and not param_info.get('singleSelection', True):
                    # 多选列表框
                    frame = ttk_bs.Frame(parent)
                    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, height=min(6, len(values)))
                    for value in values:
                        listbox.insert(tk.END, value)

                    scrollbar = ttk_bs.Scrollbar(frame, orient=tk.VERTICAL, command=listbox.yview)
                    listbox.configure(yscrollcommand=scrollbar.set)

                    listbox.pack(side=LEFT, fill=tk.BOTH, expand=True)
                    scrollbar.pack(side=RIGHT, fill=tk.Y)

                    frame.listbox = listbox  # 保存引用
                    return frame
                else:
                    # 单选下拉框
                    combo = ttk_bs.Combobox(parent, values=values, state="readonly")
                    if default_value and default_value in values:
                        combo.set(default_value)
                    elif values:
                        combo.set(values[0])
                    return combo
            else:
                # 没有选项，使用文本输入
                var = tk.StringVar(value=default_value)
                return ttk_bs.Entry(parent, textvariable=var)

        # 数字输入参数
        elif param_type in ['number', 'integer', 'float']:
            var = tk.StringVar(value=str(default_value) if default_value else '')
            entry = ttk_bs.Entry(parent, textvariable=var)

            # 添加数字验证
            def validate_number(value):
                if not value:
                    return True
                try:
                    if param_type == 'integer':
                        int(value)
                    else:
                        float(value)
                    return True
                except ValueError:
                    return False

            vcmd = (parent.register(validate_number), '%P')
            entry.config(validate='key', validatecommand=vcmd)
            return entry

        # 多行文本参数
        elif param_type in ['textarea', 'text_multiline']:
            frame = ttk_bs.Frame(parent)
            text_widget = tk.Text(frame, height=4, width=40, wrap=tk.WORD)
            if default_value:
                text_widget.insert(1.0, str(default_value))

            scrollbar = ttk_bs.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=tk.Y)

            frame.text_widget = text_widget  # 保存引用
            return frame

        # 布尔选择参数
        elif param_type in ['checkbox', 'boolean']:
            var = tk.BooleanVar()
            if default_value:
                if isinstance(default_value, str):
                    var.set(default_value.lower() in ['true', '1', 'yes', 'on'])
                else:
                    var.set(bool(default_value))

            checkbutton = ttk_bs.Checkbutton(parent, variable=var)
            checkbutton.var = var  # 保存变量引用
            return checkbutton

        # 日期时间参数
        elif param_type in ['datetime', 'date', 'time']:
            var = tk.StringVar(value=default_value)
            entry = ttk_bs.Entry(parent, textvariable=var)

            def select_datetime():
                # 这里可以添加日期时间选择器
                from tkinter import simpledialog
                result = simpledialog.askstring("日期时间", "请输入日期时间 (YYYY-MM-DD HH:MM:SS):")
                if result:
                    var.set(result)

            frame = ttk_bs.Frame(parent)
            entry = ttk_bs.Entry(frame, textvariable=var)
            entry.pack(side=LEFT, fill=X, expand=True)
            ttk_bs.Button(frame, text="选择", command=select_datetime).pack(side=RIGHT, padx=(5, 0))

            frame.var = var
            return frame

        # 颜色选择参数
        elif param_type == 'color':
            var = tk.StringVar(value=default_value or '#000000')

            def select_color():
                from tkinter import colorchooser
                color = colorchooser.askcolor(title="选择颜色")
                if color[1]:
                    var.set(color[1])

            frame = ttk_bs.Frame(parent)
            entry = ttk_bs.Entry(frame, textvariable=var, width=10)
            entry.pack(side=LEFT)

            color_button = ttk_bs.Button(frame, text="选择颜色", command=select_color)
            color_button.pack(side=LEFT, padx=(5, 0))

            frame.var = var
            return frame

        # 密码输入参数
        elif param_type == 'password':
            var = tk.StringVar(value=default_value)
            entry = ttk_bs.Entry(parent, textvariable=var, show='*')
            return entry

        # 默认文本输入参数
        else:
            var = tk.StringVar(value=str(default_value) if default_value else '')
            entry = ttk_bs.Entry(parent, textvariable=var)
            return entry
    
    def get_parameter_values(self):
        """获取参数值"""
        values = {}

        for param_name, param_data in self.param_widgets.items():
            try:
                if isinstance(param_data, dict):
                    widget = param_data['widget']
                    param_info = param_data['info']
                else:
                    widget = param_data
                    param_info = {}

                param_type = param_info.get('type', 'text')
                value = None

                # 根据参数类型获取值
                if param_type == 'file':
                    # 文件/文件夹选择
                    if hasattr(widget, 'var'):
                        value = widget.var.get()

                elif param_type in ['dropdown', 'listbox']:
                    if hasattr(widget, 'listbox'):
                        # 多选列表框
                        selected_indices = widget.listbox.curselection()
                        selected_values = [widget.listbox.get(i) for i in selected_indices]
                        delimiter = param_info.get('delimiter', ',')
                        value = delimiter.join(selected_values)
                    elif isinstance(widget, ttk_bs.Combobox):
                        # 单选下拉框
                        value = widget.get()
                    else:
                        # 文本输入
                        value = widget.get() if hasattr(widget, 'get') else ''

                elif param_type in ['textarea', 'text_multiline']:
                    # 多行文本
                    if hasattr(widget, 'text_widget'):
                        value = widget.text_widget.get(1.0, tk.END).strip()
                    elif isinstance(widget, tk.Text):
                        value = widget.get(1.0, tk.END).strip()

                elif param_type in ['checkbox', 'boolean']:
                    # 布尔值
                    if hasattr(widget, 'var'):
                        value = widget.var.get()
                    elif isinstance(widget, ttk_bs.Checkbutton):
                        value = widget.instate(['selected'])

                elif param_type in ['datetime', 'date', 'time', 'color']:
                    # 日期时间或颜色
                    if hasattr(widget, 'var'):
                        value = widget.var.get()

                elif param_type in ['number', 'integer', 'float']:
                    # 数字
                    if hasattr(widget, 'get'):
                        raw_value = widget.get()
                        if raw_value:
                            try:
                                if param_type == 'integer':
                                    value = int(raw_value)
                                elif param_type == 'float':
                                    value = float(raw_value)
                                else:
                                    value = float(raw_value)
                            except ValueError:
                                value = raw_value  # 保留原始值，让FME处理错误
                        else:
                            value = ''

                else:
                    # 默认文本输入
                    if hasattr(widget, 'get'):
                        value = widget.get()
                    elif hasattr(widget, 'var'):
                        value = widget.var.get()
                    else:
                        # 尝试获取textvariable
                        try:
                            if hasattr(widget, 'cget'):
                                var = widget.cget('textvariable')
                                if var:
                                    value = widget.tk.globalgetvar(var)
                        except:
                            pass

                # 存储参数值
                if value is not None:
                    values[param_name] = value
                    print(f"参数 {param_name} = {value} (类型: {param_type})")

            except Exception as e:
                print(f"获取参数 {param_name} 值失败: {e}")
                import traceback
                traceback.print_exc()

        return values
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 选择点击的项目
        item = self.model_tree.identify_row(event.y)
        if item:
            self.model_tree.selection_set(item)
            
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="运行模型", command=self.run_model)
            context_menu.add_command(label="解密模型", command=self.decrypt_model)
            context_menu.add_separator()
            context_menu.add_command(label="导出模型", command=self.export_model)
            context_menu.add_command(label="删除模型", command=self.delete_model)
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def on_closing(self):
        """关闭窗口事件处理"""
        # 停止所有运行中的任务
        running_tasks = self.fmw_runner.get_running_tasks()
        if running_tasks:
            if messagebox.askyesno("确认", f"有 {len(running_tasks)} 个任务正在运行，是否强制退出？"):
                for task_id in running_tasks:
                    self.fmw_runner.stop_task(task_id)
            else:
                return
        
        # 保存配置
        config.save_config()
        
        # 关闭窗口
        self.root.quit()
        self.root.destroy()
    
    def import_model(self):
        """导入模型"""
        file_path = filedialog.askopenfilename(
            title="选择FMW文件",
            filetypes=[("FMW文件", "*.fmw"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        # 获取模型信息
        dialog = ModelImportDialog(self.root, file_path)
        if dialog.result:
            model_name, description, project, category, auto_encrypt = dialog.result

            try:
                self.update_status("正在导入模型...")
                if auto_encrypt:
                    self.log_message("正在导入并加密模型...")
                else:
                    self.log_message("正在导入模型（未加密）...")

                success, message, model_id = self.model_manager.import_model(
                    file_path, model_name, description, project, category, auto_encrypt
                )

                if success:
                    self.log_message(f"模型导入成功: {message}")
                    self.refresh_models()
                    messagebox.showinfo("成功", message)
                else:
                    self.log_message(f"模型导入失败: {message}")
                    messagebox.showerror("错误", message)

            except Exception as e:
                error_msg = f"导入模型时发生错误: {e}"
                self.log_message(error_msg)
                messagebox.showerror("错误", error_msg)
            finally:
                self.update_status("就绪")

    def export_model(self):
        """导出模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        model_id = selection[0]
        model_info = self.model_manager.get_model_info(model_id)

        if not model_info:
            messagebox.showerror("错误", "模型信息不存在")
            return

        # 选择导出路径
        export_path = filedialog.asksaveasfilename(
            title="导出模型",
            defaultextension=".zip",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")],
            initialvalue=f"{model_info['name']}_export.zip"
        )

        if not export_path:
            return

        try:
            self.update_status("正在导出模型...")
            success, message = self.model_manager.export_model(model_id, export_path)

            if success:
                self.log_message(f"模型导出成功: {message}")
                messagebox.showinfo("成功", message)
            else:
                self.log_message(f"模型导出失败: {message}")
                messagebox.showerror("错误", message)

        except Exception as e:
            error_msg = f"导出模型时发生错误: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.update_statumeiyou

    def encrypt_model(self):
        """加密模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        model_id = selection[0]

        if not messagebox.askyesno("确认", "确定要加密选中的模型吗？\n加密后原文件将无法直接使用。"):
            return

        try:
            self.update_status("正在加密模型...")
            success, message = self.model_manager.encrypt_model(model_id)

            if success:
                self.log_message(f"模型加密成功: {message}")
                self.refresh_models()
                messagebox.showinfo("成功", message)
            else:
                self.log_message(f"模型加密失败: {message}")
                messagebox.showerror("错误", message)

        except Exception as e:
            error_msg = f"加密模型时发生错误: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.update_status("就绪")

    def decrypt_model(self):
        """解密模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        model_id = selection[0]
        model_info = self.model_manager.get_model_info(model_id)

        if not model_info.get("is_encrypted", False):
            messagebox.showinfo("提示", "选中的模型未加密")
            return

        # 选择解密输出路径
        output_path = filedialog.asksaveasfilename(
            title="保存解密后的FMW文件",
            defaultextension=".fmw",
            filetypes=[("FMW文件", "*.fmw"), ("所有文件", "*.*")],
            initialfile=f"{model_info['name']}_decrypted.fmw"
        )

        if not output_path:
            return

        try:
            self.update_status("正在解密模型...")
            success, message, decrypted_path = self.model_manager.decrypt_model(model_id, output_path)

            if success:
                self.log_message(f"模型解密成功: {message}")
                messagebox.showinfo("成功", f"{message}\n解密文件: {decrypted_path}")
            else:
                self.log_message(f"模型解密失败: {message}")
                messagebox.showerror("错误", message)

        except Exception as e:
            error_msg = f"解密模型时发生错误: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.update_status("就绪")

    def delete_model(self):
        """删除模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        model_id = selection[0]
        model_info = self.model_manager.get_model_info(model_id)

        if not messagebox.askyesno("确认", f"确定要删除模型 '{model_info['name']}' 吗？\n此操作不可撤销！"):
            return

        try:
            self.update_status("正在删除模型...")
            success, message = self.model_manager.delete_model(model_id)

            if success:
                self.log_message(f"模型删除成功: {message}")
                self.refresh_models()
                # 清空右侧详情显示
                self.clear_model_details()
                messagebox.showinfo("成功", message)
            else:
                self.log_message(f"模型删除失败: {message}")
                messagebox.showerror("错误", message)

        except Exception as e:
            error_msg = f"删除模型时发生错误: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.update_status("就绪")

    def clear_model_details(self):
        """清空模型详情显示"""
        # 清空详情文本
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, "请选择一个模型查看详细信息")
        self.detail_text.config(state=tk.DISABLED)

    def run_model(self):
        """运行模型"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        model_id = selection[0]
        model_info = self.model_manager.get_model_info(model_id)

        if not model_info:
            messagebox.showerror("错误", "模型信息不存在")
            return

        # 验证FME路径
        valid, message = self.fmw_runner.validate_fme_path()
        if not valid:
            messagebox.showerror("错误", f"FME路径配置错误: {message}")
            return

        # 获取参数值
        parameters = self.get_parameter_values()

        # 创建输出目录
        output_dir = os.path.join(config.get("output_dir"), model_id,
                                 datetime.now().strftime("%Y%m%d_%H%M%S"))
        os.makedirs(output_dir, exist_ok=True)

        # 获取运行时FMW文件路径（自动处理加密/解密）
        fmw_path = self.model_manager.get_runtime_fmw_path(model_id)
        if not fmw_path:
            messagebox.showerror("错误", "无法获取模型文件路径")
            return

        # 如果是加密模型，显示解密状态
        if model_info.get("is_encrypted", False):
            self.log_message(f"模型已加密，正在使用临时解密文件运行")
            self.log_message(f"临时文件路径: {fmw_path}")

        # 创建任务
        task_id = self.task_manager.create_task(fmw_path, parameters, output_dir)

        # 定义回调函数
        def progress_callback(task_id, message):
            self.root.after(0, lambda: self.log_message(f"[{task_id}] {message}"))

        def complete_callback(result):
            self.root.after(0, lambda: self.on_task_complete(result))

        try:
            self.update_status(f"正在运行模型: {model_info['name']}")
            self.progress_bar.start()
            self.log_message(f"开始运行模型: {model_info['name']}")

            # 更新任务状态
            self.task_manager.update_task_status(task_id, 'running')

            # 异步运行模型
            self.fmw_runner.run_fmw_async(
                task_id, fmw_path, parameters, output_dir,
                progress_callback, complete_callback
            )

            # 更新模型使用统计
            self.model_manager.update_model_usage(model_id)

        except Exception as e:
            error_msg = f"运行模型时发生错误: {e}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            self.progress_bar.stop()
            self.update_status("就绪")

    def on_task_complete(self, result):
        """任务完成回调"""
        self.progress_bar.stop()

        task_id = result.get('task_id', '')
        success = result.get('success', False)

        if success:
            execution_time = result.get('execution_time', 0)
            output_dir = result.get('output_dir', '')

            self.log_message(f"任务 {task_id} 完成，耗时: {execution_time:.2f}秒")
            self.log_message(f"输出目录: {output_dir}")

            self.task_manager.update_task_status(task_id, 'completed', result)
            self.update_status("模型运行完成")

            # 询问是否打开输出目录
            if messagebox.askyesno("完成", f"模型运行完成！\n耗时: {execution_time:.2f}秒\n\n是否打开输出目录？"):
                try:
                    os.startfile(output_dir)
                except:
                    pass
        else:
            error = result.get('error', result.get('stderr', '未知错误'))
            self.log_message(f"任务 {task_id} 失败: {error}")

            self.task_manager.update_task_status(task_id, 'failed', result)
            self.update_status("模型运行失败")

            messagebox.showerror("错误", f"模型运行失败:\n{error}")

    def stop_task(self):
        """停止任务"""
        running_tasks = self.fmw_runner.get_running_tasks()
        if not running_tasks:
            messagebox.showinfo("提示", "当前没有运行中的任务")
            return

        # 如果只有一个任务，直接停止
        if len(running_tasks) == 1:
            task_id = running_tasks[0]
            if messagebox.askyesno("确认", f"确定要停止任务 {task_id} 吗？"):
                success, message = self.fmw_runner.stop_task(task_id)
                if success:
                    self.log_message(f"任务 {task_id} 已停止")
                    self.task_manager.update_task_status(task_id, 'stopped')
                    self.progress_bar.stop()
                    self.update_status("任务已停止")
                else:
                    messagebox.showerror("错误", f"停止任务失败: {message}")
        else:
            # 多个任务，显示选择对话框
            TaskStopDialog(self.root, running_tasks, self.fmw_runner, self.task_manager)

    def show_task_history(self):
        """显示任务历史"""
        TaskHistoryDialog(self.root, self.task_manager)

    def create_client(self):
        """创建分发客户端"""
        models = self.model_manager.get_all_models()
        if not models:
            messagebox.showwarning("警告", "没有可用的模型，请先导入模型")
            return

        ClientCreateDialog(self.root, models, self.client_generator)

    def generate_registration_code(self):
        """生成注册码"""
        from dialogs import RegistrationCodeDialog

        dialog = RegistrationCodeDialog(self.root)
        if dialog.result:
            # 注册码已生成，显示在对话框中
            pass

    def open_multi_model_license_generator(self):
        """打开多模型许可证生成器"""
        try:
            from multi_model_license_generator import MultiModelLicenseGenerator

            generator = MultiModelLicenseGenerator(self.root)

        except Exception as e:
            messagebox.showerror("错误", f"打开多模型许可证生成器失败: {e}")

    def install_pyinstaller(self):
        """安装PyInstaller"""
        try:
            import subprocess
            import sys

            # 显示确认对话框
            result = messagebox.askyesno(
                "安装PyInstaller",
                "PyInstaller用于将客户端打包成exe文件。\n\n是否要安装PyInstaller？\n\n注意：这可能需要几分钟时间。"
            )

            if not result:
                return

            # 显示进度对话框
            progress_window = tk.Toplevel(self.root)
            progress_window.title("安装PyInstaller")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            progress_window.transient(self.root)
            progress_window.grab_set()

            # 居中显示
            progress_window.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            # 进度标签
            progress_label = tk.Label(progress_window, text="正在安装PyInstaller，请稍候...")
            progress_label.pack(pady=20)

            # 进度条
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            def install_in_thread():
                try:
                    # 检查是否已安装
                    try:
                        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"],
                                              capture_output=True, text=True, check=True)
                        # 已安装
                        progress_window.after(0, lambda: show_result(True, f"PyInstaller已安装，版本: {result.stdout.strip()}"))
                        return
                    except:
                        pass

                    # 安装PyInstaller
                    subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"],
                                  check=True, capture_output=True)

                    # 验证安装
                    result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"],
                                          capture_output=True, text=True, check=True)

                    progress_window.after(0, lambda: show_result(True, f"PyInstaller安装成功！版本: {result.stdout.strip()}"))

                except subprocess.CalledProcessError as e:
                    progress_window.after(0, lambda: show_result(False, f"安装失败: {e}"))
                except Exception as e:
                    progress_window.after(0, lambda: show_result(False, f"安装过程中发生错误: {e}"))

            def show_result(success, message):
                progress_bar.stop()
                progress_window.destroy()

                if success:
                    messagebox.showinfo("安装成功", message)
                else:
                    messagebox.showerror("安装失败", f"{message}\n\n请手动运行: pip install pyinstaller")

            # 在后台线程中执行安装
            import threading
            install_thread = threading.Thread(target=install_in_thread)
            install_thread.daemon = True
            install_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"安装PyInstaller时发生错误: {e}")

    def clean_temp_files(self):
        """清理临时文件"""
        try:
            import shutil

            temp_dir = config.get_absolute_path(config.get("temp_dir"))

            if os.path.exists(temp_dir):
                # 计算临时文件大小
                total_size = 0
                file_count = 0

                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                            file_count += 1
                        except:
                            pass

                if file_count == 0:
                    messagebox.showinfo("清理临时文件", "没有找到临时文件。")
                    return

                # 显示确认对话框
                size_mb = total_size / (1024 * 1024)
                result = messagebox.askyesno(
                    "清理临时文件",
                    f"找到 {file_count} 个临时文件，总大小 {size_mb:.2f} MB。\n\n是否要删除这些文件？"
                )

                if result:
                    # 清理临时文件
                    shutil.rmtree(temp_dir)
                    os.makedirs(temp_dir, exist_ok=True)

                    messagebox.showinfo("清理完成", f"已清理 {file_count} 个临时文件，释放 {size_mb:.2f} MB 空间。")
            else:
                messagebox.showinfo("清理临时文件", "临时文件目录不存在。")

        except Exception as e:
            messagebox.showerror("错误", f"清理临时文件时发生错误: {e}")

    def manage_clients(self):
        """管理客户端"""
        ClientManageDialog(self.root, self.client_generator)

    def open_settings(self):
        """打开设置"""
        SettingsDialog(self.root)

    def show_about(self):
        """显示关于信息"""
        about_text = f"""FME模型管理工具 v{config.get('app_version', '1.0.0')}

一款功能强大的FME模型管理工具，提供以下功能：
• FMW文件导入和管理
• 模型参数解析和配置
• 模型加密和解密
• 模型运行和任务管理
• 分发客户端生成
• 使用限制和许可证管理

开发者: {config.get('app_author', 'FME Tools')}
"""
        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行主窗口"""
        self.root.mainloop()
