"""
多模型许可证生成器
支持为多个FMW模型生成统一的许可证文件
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import json
from datetime import datetime, timedelta

from encryption import LicenseManager
from model_manager import ModelManager


class MultiModelLicenseGenerator:
    """多模型许可证生成器"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.license_manager = LicenseManager()
        self.model_manager = ModelManager()
        self.selected_models = {}  # {model_id: license_config}
        
        self.create_window()
        
    def create_window(self):
        """创建窗口"""
        if self.parent:
            self.window = tk.Toplevel(self.parent)
        else:
            self.window = tk.Tk()
            
        self.window.title("多模型许可证生成器")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk_bs.Label(
            main_frame, 
            text="多模型许可证生成器", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 客户端信息框架
        client_frame = ttk_bs.LabelFrame(main_frame, text="客户端信息", padding=10)
        client_frame.pack(fill=X, pady=(0, 10))
        
        # 客户端名称
        ttk_bs.Label(client_frame, text="客户端名称:").grid(row=0, column=0, sticky=W, padx=(0, 10))
        self.client_name_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.client_name_var, width=30).grid(row=0, column=1, sticky=W)
        
        # 机器码
        ttk_bs.Label(client_frame, text="机器码:").grid(row=1, column=0, sticky=W, padx=(0, 10), pady=(10, 0))
        self.machine_id_var = tk.StringVar()
        ttk_bs.Entry(client_frame, textvariable=self.machine_id_var, width=40).grid(row=1, column=1, sticky=W, pady=(10, 0))
        
        # 模型选择框架
        models_frame = ttk_bs.LabelFrame(main_frame, text="模型选择与配置", padding=10)
        models_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # 可用模型列表
        left_frame = ttk_bs.Frame(models_frame)
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        ttk_bs.Label(left_frame, text="可用模型:").pack(anchor=W)
        
        # 模型列表
        self.models_listbox = tk.Listbox(left_frame, height=15)
        self.models_listbox.pack(fill=BOTH, expand=True, pady=(5, 0))
        
        # 加载模型列表
        self.load_models()
        
        # 中间按钮
        middle_frame = ttk_bs.Frame(models_frame)
        middle_frame.pack(side=LEFT, padx=10)
        
        ttk_bs.Button(middle_frame, text="添加 →", command=self.add_model).pack(pady=5)
        ttk_bs.Button(middle_frame, text="← 移除", command=self.remove_model).pack(pady=5)
        ttk_bs.Button(middle_frame, text="配置", command=self.configure_model).pack(pady=5)
        
        # 已选模型列表
        right_frame = ttk_bs.Frame(models_frame)
        right_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0))
        
        ttk_bs.Label(right_frame, text="已选模型:").pack(anchor=W)
        
        # 已选模型树形视图
        columns = ("模型名称", "过期时间", "使用次数")
        self.selected_tree = ttk.Treeview(right_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.selected_tree.heading("#0", text="模型ID")
        for col in columns:
            self.selected_tree.heading(col, text=col)
            
        # 设置列宽
        self.selected_tree.column("#0", width=100)
        self.selected_tree.column("模型名称", width=150)
        self.selected_tree.column("过期时间", width=100)
        self.selected_tree.column("使用次数", width=80)
        
        self.selected_tree.pack(fill=BOTH, expand=True, pady=(5, 0))
        
        # 操作按钮框架
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(button_frame, text="生成许可证文件", command=self.generate_license_file).pack(side=LEFT, padx=(0, 10))
        ttk_bs.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=LEFT, padx=(0, 10))
        ttk_bs.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=RIGHT)
        
    def load_models(self):
        """加载可用模型列表"""
        self.models_listbox.delete(0, tk.END)
        
        models = self.model_manager.get_all_models()
        for model_id, model_info in models.items():
            display_text = f"{model_info['name']} ({model_id})"
            self.models_listbox.insert(tk.END, display_text)
            
    def add_model(self):
        """添加模型到选择列表"""
        selection = self.models_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个模型")
            return
            
        # 获取选中的模型
        selected_text = self.models_listbox.get(selection[0])
        model_id = selected_text.split('(')[-1].rstrip(')')
        
        if model_id in self.selected_models:
            messagebox.showinfo("提示", "该模型已经添加")
            return
            
        # 获取模型信息
        models = self.model_manager.get_all_models()
        model_info = models.get(model_id)
        
        if not model_info:
            messagebox.showerror("错误", "模型信息不存在")
            return
            
        # 添加默认配置
        self.selected_models[model_id] = {
            'model_name': model_info['name'],
            'expire_days': None,
            'max_uses': None
        }
        
        self.update_selected_tree()
        
    def remove_model(self):
        """从选择列表移除模型"""
        selection = self.selected_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移除的模型")
            return
            
        item = selection[0]
        model_id = self.selected_tree.item(item)['text']
        
        if model_id in self.selected_models:
            del self.selected_models[model_id]
            self.update_selected_tree()
            
    def configure_model(self):
        """配置模型许可证参数"""
        selection = self.selected_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要配置的模型")
            return
            
        item = selection[0]
        model_id = self.selected_tree.item(item)['text']
        
        if model_id not in self.selected_models:
            return
            
        # 打开配置对话框
        config_dialog = ModelLicenseConfigDialog(self.window, model_id, self.selected_models[model_id])
        if config_dialog.result:
            self.selected_models[model_id] = config_dialog.result
            self.update_selected_tree()
            
    def update_selected_tree(self):
        """更新已选模型树形视图"""
        # 清空现有项目
        for item in self.selected_tree.get_children():
            self.selected_tree.delete(item)
            
        # 添加已选模型
        for model_id, config in self.selected_models.items():
            expire_text = f"{config['expire_days']}天" if config['expire_days'] else "无限制"
            uses_text = str(config['max_uses']) if config['max_uses'] else "无限制"
            
            self.selected_tree.insert("", tk.END, text=model_id, values=(
                config['model_name'],
                expire_text,
                uses_text
            ))
            
    def clear_selection(self):
        """清空选择"""
        self.selected_models.clear()
        self.update_selected_tree()
        
    def generate_license_file(self):
        """生成许可证文件"""
        if not self.selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return
            
        client_name = self.client_name_var.get().strip()
        machine_id = self.machine_id_var.get().strip()
        
        if not client_name:
            messagebox.showwarning("警告", "请输入客户端名称")
            return
            
        if not machine_id:
            messagebox.showwarning("警告", "请输入机器码")
            return
            
        if len(machine_id) != 32:
            messagebox.showwarning("警告", "机器码格式不正确（应为32位十六进制字符串）")
            return
            
        try:
            # 为每个模型生成许可证
            model_licenses = {}
            
            for model_id, config in self.selected_models.items():
                expire_date = None
                if config['expire_days']:
                    expire_date = datetime.now() + timedelta(days=config['expire_days'])
                    
                license_data = self.license_manager.generate_model_license(
                    model_id=model_id,
                    model_name=config['model_name'],
                    expire_date=expire_date,
                    max_uses=config['max_uses'],
                    allowed_machines=[machine_id]
                )
                
                model_licenses[model_id] = license_data
                
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="保存许可证文件",
                defaultextension=".lic",
                filetypes=[("许可证文件", "*.lic"), ("所有文件", "*.*")],
                initialfile=f"{client_name}_license.lic"
            )
            
            if file_path:
                # 保存多模型许可证文件
                success = self.license_manager.save_multi_model_license(model_licenses, file_path)
                
                if success:
                    messagebox.showinfo("成功", f"许可证文件已生成: {file_path}")
                else:
                    messagebox.showerror("错误", "生成许可证文件失败")
                    
        except Exception as e:
            messagebox.showerror("错误", f"生成许可证文件时发生错误: {e}")


class ModelLicenseConfigDialog:
    """模型许可证配置对话框"""
    
    def __init__(self, parent, model_id, current_config):
        self.parent = parent
        self.model_id = model_id
        self.current_config = current_config.copy()
        self.result = None
        
        self.create_dialog()
        
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"配置模型许可证 - {self.model_id}")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 过期时间设置
        expire_frame = ttk_bs.LabelFrame(main_frame, text="过期时间设置", padding=10)
        expire_frame.pack(fill=X, pady=(0, 10))
        
        self.enable_expire_var = tk.BooleanVar(value=bool(self.current_config.get('expire_days')))
        ttk_bs.Checkbutton(
            expire_frame, 
            text="启用过期时间限制", 
            variable=self.enable_expire_var,
            command=self.toggle_expire
        ).pack(anchor=W)
        
        expire_input_frame = ttk_bs.Frame(expire_frame)
        expire_input_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Label(expire_input_frame, text="过期天数:").pack(side=LEFT)
        self.expire_days_var = tk.StringVar(value=str(self.current_config.get('expire_days', 30)))
        self.expire_days_entry = ttk_bs.Entry(expire_input_frame, textvariable=self.expire_days_var, width=10)
        self.expire_days_entry.pack(side=LEFT, padx=(10, 0))
        
        # 使用次数设置
        usage_frame = ttk_bs.LabelFrame(main_frame, text="使用次数设置", padding=10)
        usage_frame.pack(fill=X, pady=(0, 10))
        
        self.enable_usage_var = tk.BooleanVar(value=bool(self.current_config.get('max_uses')))
        ttk_bs.Checkbutton(
            usage_frame, 
            text="启用使用次数限制", 
            variable=self.enable_usage_var,
            command=self.toggle_usage
        ).pack(anchor=W)
        
        usage_input_frame = ttk_bs.Frame(usage_frame)
        usage_input_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Label(usage_input_frame, text="最大使用次数:").pack(side=LEFT)
        self.max_uses_var = tk.StringVar(value=str(self.current_config.get('max_uses', 100)))
        self.max_uses_entry = ttk_bs.Entry(usage_input_frame, textvariable=self.max_uses_var, width=10)
        self.max_uses_entry.pack(side=LEFT, padx=(10, 0))
        
        # 按钮
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))
        
        ttk_bs.Button(button_frame, text="确定", command=self.save_config).pack(side=RIGHT, padx=(10, 0))
        ttk_bs.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=RIGHT)
        
        # 初始状态
        self.toggle_expire()
        self.toggle_usage()
        
    def toggle_expire(self):
        """切换过期时间设置"""
        if self.enable_expire_var.get():
            self.expire_days_entry.config(state=tk.NORMAL)
        else:
            self.expire_days_entry.config(state=tk.DISABLED)
            
    def toggle_usage(self):
        """切换使用次数设置"""
        if self.enable_usage_var.get():
            self.max_uses_entry.config(state=tk.NORMAL)
        else:
            self.max_uses_entry.config(state=tk.DISABLED)
            
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'model_name': self.current_config['model_name'],
                'expire_days': None,
                'max_uses': None
            }
            
            # 过期时间
            if self.enable_expire_var.get():
                expire_days = int(self.expire_days_var.get())
                if expire_days <= 0:
                    raise ValueError("过期天数必须大于0")
                config['expire_days'] = expire_days
                
            # 使用次数
            if self.enable_usage_var.get():
                max_uses = int(self.max_uses_var.get())
                if max_uses <= 0:
                    raise ValueError("使用次数必须大于0")
                config['max_uses'] = max_uses
                
            self.result = config
            self.dialog.destroy()
            
        except ValueError as e:
            messagebox.showerror("错误", f"配置参数错误: {e}")


if __name__ == "__main__":
    app = MultiModelLicenseGenerator()
    app.window.mainloop()
